import { Create<PERSON>ser<PERSON>ommandHandler } from './create-user.command';
import { UpdateUserCommandHandler } from './update-user.command';
import { ChangePasswordCommandHandler } from './change-password.command';
import { DeleteUserCommandHandler } from './delete-user.command';
import { UpdateProfileStatsCommandHandler, RecordProfileViewCommandHandler } from './update-profile-stats.command';

export const CommandHandlers = [
  Create<PERSON><PERSON><PERSON>ommandHandler,
  UpdateUserCommandHandler,
  ChangePassword<PERSON>ommandHandler,
  DeleteUserCommandHandler,
  UpdateProfileStatsCommandHandler,
  RecordProfileViewCommandHandler,
];
