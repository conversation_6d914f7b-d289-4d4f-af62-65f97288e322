// Migration for profile statistics and views tracking
module.exports = async (client, schema) => {
  // Create profile_stats table
  await client.query(`CREATE TABLE IF NOT EXISTS ${schema}."profile_stats" (
    "user_id" bigint PRIMARY KEY REFERENCES ${schema}."users"("user_id") ON DELETE CASCADE,
    "completion_percentage" integer DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    "profile_views" integer DEFAULT 0 CHECK (profile_views >= 0),
    "posts_count" integer DEFAULT 0 CHECK (posts_count >= 0),
    "followers_count" integer DEFAULT 0 CHECK (followers_count >= 0),
    "following_count" integer DEFAULT 0 CHECK (following_count >= 0),
    "groups_count" integer DEFAULT 0 CHECK (groups_count >= 0),
    "trips_count" integer DEFAULT 0 CHECK (trips_count >= 0),
    "last_active" timestamp(0) without time zone DEFAULT current_timestamp,
    "created_at" timestamp(0) without time zone DEFAULT current_timestamp,
    "updated_at" timestamp(0) without time zone DEFAULT current_timestamp
  );`);

  // Create profile_views table for tracking who viewed whose profile
  await client.query(`CREATE TABLE IF NOT EXISTS ${schema}."profile_views" (
    "view_id" bigserial PRIMARY KEY,
    "viewer_id" bigint NOT NULL REFERENCES ${schema}."users"("user_id") ON DELETE CASCADE,
    "profile_owner_id" bigint NOT NULL REFERENCES ${schema}."users"("user_id") ON DELETE CASCADE,
    "viewed_at" timestamp(0) without time zone DEFAULT current_timestamp,
    UNIQUE("viewer_id", "profile_owner_id")
  );`);

  // Create indexes for better performance
  await client.query(`CREATE INDEX IF NOT EXISTS idx_profile_stats_user_id ON ${schema}."profile_stats"("user_id");`);
  await client.query(`CREATE INDEX IF NOT EXISTS idx_profile_stats_completion ON ${schema}."profile_stats"("completion_percentage");`);
  await client.query(`CREATE INDEX IF NOT EXISTS idx_profile_stats_last_active ON ${schema}."profile_stats"("last_active");`);
  
  await client.query(`CREATE INDEX IF NOT EXISTS idx_profile_views_viewer ON ${schema}."profile_views"("viewer_id");`);
  await client.query(`CREATE INDEX IF NOT EXISTS idx_profile_views_owner ON ${schema}."profile_views"("profile_owner_id");`);
  await client.query(`CREATE INDEX IF NOT EXISTS idx_profile_views_viewed_at ON ${schema}."profile_views"("viewed_at");`);

  // Create trigger to automatically update updated_at timestamp
  await client.query(`
    CREATE OR REPLACE FUNCTION ${schema}.update_profile_stats_updated_at()
    RETURNS TRIGGER AS $$
    BEGIN
      NEW.updated_at = current_timestamp;
      RETURN NEW;
    END;
    $$ language 'plpgsql';
  `);

  await client.query(`
    DROP TRIGGER IF EXISTS trigger_update_profile_stats_updated_at ON ${schema}."profile_stats";
    CREATE TRIGGER trigger_update_profile_stats_updated_at
      BEFORE UPDATE ON ${schema}."profile_stats"
      FOR EACH ROW
      EXECUTE FUNCTION ${schema}.update_profile_stats_updated_at();
  `);
};
