import { FindByIDQueryHandler } from './find-by-id.query';
import { FindByUsernameQueryHandler } from './find-by-username.query';
import { GetUserDetailsQueryHandler } from './get-user-details.query';
import { SearchUsersQueryHandler } from './search-users.query';
import { GetProfileStatsQueryHandler } from './get-profile-stats.query';

export const QueryHandlers = [
  Find<PERSON>yUsername<PERSON><PERSON>y<PERSON>and<PERSON>,
  FindByIDQueryHandler,
  GetUserDetailsQueryHandler,
  SearchUsersQueryHandler,
  GetProfileStatsQueryHandler,
];
